using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Dynamic reward system for PUBG-style loot and combat training
/// Provides contextual rewards based on game state and tactical decisions
/// </summary>
public class DynamicRewardSystem : MonoBehaviour
{
    [Header("🎯 Base Reward Values")]
    public float basicWeaponReward = 0.1f;
    public float arWithScopeReward = 0.3f;
    public float healingItemReward = 0.2f;
    public float level3GearReward = 0.4f;
    public float utilityWhenDownedReward = 0.2f;
    
    [Header("🏆 Combat Rewards")]
    public float enemyKillReward = 0.5f;
    public float headshotBonusReward = 0.2f;
    public float tacticalKillReward = 0.3f; // Using cover, flanking, etc.
    public float teamworkReward = 0.25f; // Helping downed player
    
    [Header("🧠 Tactical Rewards")]
    public float coverUsageReward = 0.15f;
    public float flankingReward = 0.2f;
    public float retreatWhenLowReward = 0.1f;
    public float healingTimingReward = 0.2f;
    public float weaponUpgradeReward = 0.15f;
    
    [Header("⚖️ Penalty Values")]
    public float damageTakenPenalty = -0.1f;
    public float deathPenalty = -1f;
    public float badPositioningPenalty = -0.05f;
    public float wastedResourcesPenalty = -0.1f;

    private SquadMateAgent agent;
    private PlayerController player;
    private Dictionary<string, float> rewardHistory = new Dictionary<string, float>();
    private float totalRewardsGiven = 0f;

    void Start()
    {
        agent = GetComponent<SquadMateAgent>();
        player = FindObjectOfType<PlayerController>();
        
        if (agent == null)
        {
            Debug.LogError("DynamicRewardSystem requires SquadMateAgent component!");
        }
    }

    #region Loot Rewards

    /// <summary>
    /// Calculate reward for picking up a weapon
    /// </summary>
    public void RewardWeaponPickup(WeaponLoot weapon)
    {
        if (agent == null) return;
        
        float reward = basicWeaponReward;
        string rewardReason = $"Weapon pickup: {weapon.itemName}";
        
        // Bonus for weapon tier
        switch (weapon.weaponTier)
        {
            case WeaponLoot.WeaponTier.Tier2:
                reward *= 1.5f;
                break;
            case WeaponLoot.WeaponTier.Tier3:
                reward *= 2f;
                break;
            case WeaponLoot.WeaponTier.Crate:
                reward *= 3f;
                break;
        }
        
        // Bonus for assault rifles (most versatile)
        if (weapon.weaponClass == WeaponLoot.WeaponClass.AssaultRifle)
        {
            reward += 0.05f;
            rewardReason += " (AR bonus)";
        }
        
        // Check if agent has scope for AR bonus
        if (weapon.weaponClass == WeaponLoot.WeaponClass.AssaultRifle && HasScope())
        {
            reward = arWithScopeReward;
            rewardReason += " (AR + Scope combo!)";
        }
        
        // Bonus for weapon upgrade
        if (IsWeaponUpgrade(weapon))
        {
            reward += weaponUpgradeReward;
            rewardReason += " (Upgrade bonus)";
        }
        
        GiveReward(reward, rewardReason);
    }

    /// <summary>
    /// Calculate reward for picking up healing items
    /// </summary>
    public void RewardHealingPickup(HealingLoot healing)
    {
        if (agent == null) return;
        
        float reward = healingItemReward;
        string rewardReason = $"Healing pickup: {healing.itemName}";
        
        // Higher reward when injured
        float healthPercent = agent.currentHealth / agent.maxHealth;
        if (healthPercent < 0.3f)
        {
            reward *= 2f;
            rewardReason += " (Critical health)";
        }
        else if (healthPercent < 0.6f)
        {
            reward *= 1.5f;
            rewardReason += " (Low health)";
        }
        
        // Bonus for high-tier healing
        if (healing.healingType == HealingLoot.HealingType.MedKit)
        {
            reward *= 1.3f;
            rewardReason += " (MedKit bonus)";
        }
        
        GiveReward(reward, rewardReason);
    }

    /// <summary>
    /// Reward for picking up utility items when teammate is downed
    /// </summary>
    public void RewardUtilityWhenTeammateDown(LootItem utility)
    {
        if (agent == null || player == null) return;
        
        if (player.isDowned)
        {
            float reward = utilityWhenDownedReward;
            string rewardReason = $"Utility pickup while teammate down: {utility.itemName}";
            
            // Extra bonus for smoke grenades (good for revives)
            if (utility.itemName.Contains("Smoke"))
            {
                reward *= 1.5f;
                rewardReason += " (Smoke for revive)";
            }
            
            GiveReward(reward, rewardReason);
        }
    }

    #endregion

    #region Combat Rewards

    /// <summary>
    /// Reward for eliminating an enemy
    /// </summary>
    public void RewardEnemyKill(bool wasHeadshot = false, bool wasTactical = false)
    {
        if (agent == null) return;
        
        float reward = enemyKillReward;
        string rewardReason = "Enemy eliminated";
        
        if (wasHeadshot)
        {
            reward += headshotBonusReward;
            rewardReason += " (HEADSHOT!)";
        }
        
        if (wasTactical)
        {
            reward += tacticalKillReward;
            rewardReason += " (Tactical)";
        }
        
        // Bonus if protecting downed teammate
        if (player != null && player.isDowned)
        {
            reward += teamworkReward;
            rewardReason += " (Protecting teammate)";
        }
        
        GiveReward(reward, rewardReason);
    }

    /// <summary>
    /// Reward for using cover effectively
    /// </summary>
    public void RewardCoverUsage()
    {
        if (agent == null) return;
        
        // Only reward if in combat
        if (agent.isInCombat)
        {
            GiveReward(coverUsageReward, "Effective cover usage");
        }
    }

    /// <summary>
    /// Reward for flanking maneuvers
    /// </summary>
    public void RewardFlanking()
    {
        if (agent == null) return;
        
        GiveReward(flankingReward, "Flanking maneuver");
    }

    /// <summary>
    /// Reward for tactical retreat when low health
    /// </summary>
    public void RewardTacticalRetreat()
    {
        if (agent == null) return;
        
        float healthPercent = agent.currentHealth / agent.maxHealth;
        if (healthPercent < 0.3f && agent.isInCombat)
        {
            GiveReward(retreatWhenLowReward, "Tactical retreat (low health)");
        }
    }

    /// <summary>
    /// Reward for healing at appropriate times
    /// </summary>
    public void RewardHealingTiming()
    {
        if (agent == null) return;
        
        // Reward healing when not in immediate danger
        if (!agent.isInCombat && agent.currentHealth < agent.maxHealth * 0.8f)
        {
            GiveReward(healingTimingReward, "Good healing timing");
        }
    }

    #endregion

    #region Penalties

    /// <summary>
    /// Apply penalty for taking damage
    /// </summary>
    public void PenalizeDamageTaken(float damageAmount)
    {
        if (agent == null) return;
        
        float penalty = damageTakenPenalty * (damageAmount / agent.maxHealth);
        GivePenalty(penalty, $"Damage taken: {damageAmount:F1}");
    }

    /// <summary>
    /// Apply penalty for death
    /// </summary>
    public void PenalizeDeath()
    {
        if (agent == null) return;
        
        GivePenalty(deathPenalty, "Agent death");
    }

    /// <summary>
    /// Apply penalty for bad positioning
    /// </summary>
    public void PenalizeBadPositioning()
    {
        if (agent == null) return;
        
        GivePenalty(badPositioningPenalty, "Poor positioning");
    }

    /// <summary>
    /// Apply penalty for wasting resources
    /// </summary>
    public void PenalizeWastedResources(string resourceType)
    {
        if (agent == null) return;
        
        GivePenalty(wastedResourcesPenalty, $"Wasted resource: {resourceType}");
    }

    #endregion

    #region Helper Methods

    bool HasScope()
    {
        // Check if agent has scope attachment (implement based on your attachment system)
        // For now, return false as placeholder
        return false;
    }

    bool IsWeaponUpgrade(WeaponLoot newWeapon)
    {
        // Check if new weapon is better than current weapon
        WeaponSystem currentWeapon = agent.GetComponent<WeaponSystem>();
        if (currentWeapon == null || !currentWeapon.HasWeapon())
        {
            return true; // Any weapon is an upgrade from no weapon
        }
        
        // Simple tier comparison (implement more sophisticated logic as needed)
        return newWeapon.weaponTier >= WeaponLoot.WeaponTier.Tier2;
    }

    void GiveReward(float amount, string reason)
    {
        if (agent == null) return;
        
        agent.AddReward(amount);
        totalRewardsGiven += amount;
        
        // Track reward history
        if (rewardHistory.ContainsKey(reason))
        {
            rewardHistory[reason] += amount;
        }
        else
        {
            rewardHistory[reason] = amount;
        }
        
        Debug.Log($"🎯 REWARD: +{amount:F3} - {reason}");
    }

    void GivePenalty(float amount, string reason)
    {
        if (agent == null) return;
        
        agent.AddReward(amount); // amount is already negative
        totalRewardsGiven += amount;
        
        // Track penalty history
        if (rewardHistory.ContainsKey(reason))
        {
            rewardHistory[reason] += amount;
        }
        else
        {
            rewardHistory[reason] = amount;
        }
        
        Debug.Log($"❌ PENALTY: {amount:F3} - {reason}");
    }

    #endregion

    #region Public Interface

    /// <summary>
    /// Get total rewards given this episode
    /// </summary>
    public float GetTotalRewards()
    {
        return totalRewardsGiven;
    }

    /// <summary>
    /// Get reward breakdown for analysis
    /// </summary>
    public Dictionary<string, float> GetRewardHistory()
    {
        return new Dictionary<string, float>(rewardHistory);
    }

    /// <summary>
    /// Reset reward tracking for new episode
    /// </summary>
    public void ResetRewards()
    {
        totalRewardsGiven = 0f;
        rewardHistory.Clear();
    }

    #endregion

    void OnGUI()
    {
        if (!Application.isPlaying) return;
        
        // Display reward summary in top-right corner
        GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🎯 Dynamic Rewards", EditorStyles.boldLabel);
        GUILayout.Label($"Total: {totalRewardsGiven:F2}");
        
        GUILayout.Space(5);
        GUILayout.Label("Recent Rewards:");
        
        int count = 0;
        foreach (var kvp in rewardHistory)
        {
            if (count >= 5) break; // Show only last 5
            
            Color color = kvp.Value >= 0 ? Color.green : Color.red;
            GUI.color = color;
            GUILayout.Label($"{kvp.Key}: {kvp.Value:F2}");
            count++;
        }
        
        GUI.color = Color.white;
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
