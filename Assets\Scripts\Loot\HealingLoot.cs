using UnityEngine;

/// <summary>
/// Healing loot items (MedKit, FirstAidKit, EnergyDrink, etc.)
/// </summary>
public class HealingLoot : LootItem
{
    [Header("💊 Healing Stats")]
    public HealingType healingType = HealingType.MedKit;
    public float healAmount = 100f;
    public float useTime = 8f; // Time to use item
    public float maxHealthPercent = 1f; // Max health % this item can heal to
    public bool canUseInCombat = false;
    public bool isInstantHeal = false;

    [Header("🔋 Boost Effects")]
    public bool providesBoost = false;
    public float boostAmount = 0f; // For energy drinks/painkillers
    public float boostDuration = 0f;

    public enum HealingType
    {
        Bandage,
        FirstAidKit,
        MedKit,
        EnergyDrink,
        Painkiller,
        AdrenalineSyringe
    }

    protected override void Start()
    {
        base.Start();
        lootType = LootType.Healing;

        // Set healing-specific values
        SetHealingValues();
    }

    void SetHealingValues()
    {
        switch (healingType)
        {
            case HealingType.Bandage:
                healAmount = 10f;
                useTime = 4f;
                maxHealthPercent = 0.75f;
                rewardValue = 0.05f;
                itemValue = 0.5f;
                rarity = LootRarity.Common;
                canStack = true;
                maxStackSize = 5;
                break;

            case HealingType.FirstAidKit:
                healAmount = 75f;
                useTime = 6f;
                maxHealthPercent = 0.75f;
                rewardValue = 0.15f;
                itemValue = 1.5f;
                rarity = LootRarity.Uncommon;
                break;

            case HealingType.MedKit:
                healAmount = 100f;
                useTime = 8f;
                maxHealthPercent = 1f;
                rewardValue = 0.25f;
                itemValue = 3f;
                rarity = LootRarity.Rare;
                break;

            case HealingType.EnergyDrink:
                healAmount = 0f;
                boostAmount = 40f;
                boostDuration = 60f;
                useTime = 4f;
                providesBoost = true;
                rewardValue = 0.1f;
                itemValue = 1f;
                rarity = LootRarity.Common;
                canStack = true;
                maxStackSize = 3;
                break;

            case HealingType.Painkiller:
                healAmount = 0f;
                boostAmount = 60f;
                boostDuration = 90f;
                useTime = 6f;
                providesBoost = true;
                rewardValue = 0.15f;
                itemValue = 1.5f;
                rarity = LootRarity.Uncommon;
                canStack = true;
                maxStackSize = 2;
                break;

            case HealingType.AdrenalineSyringe:
                healAmount = 0f;
                boostAmount = 100f;
                boostDuration = 120f;
                useTime = 6f;
                providesBoost = true;
                rewardValue = 0.3f;
                itemValue = 4f;
                rarity = LootRarity.Epic;
                break;
        }
    }

    protected override bool CanPickup(GameObject picker)
    {
        // Check if picker needs healing or boost
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            // Always allow pickup for inventory management
            return true;
        }

        PlayerController player = picker.GetComponent<PlayerController>();
        if (player != null)
        {
            // Check if player needs healing
            if (!providesBoost && player.currentHealth >= player.maxHealth * maxHealthPercent)
            {
                return false; // Can't heal beyond max for this item
            }
            return true;
        }

        return true;
    }

    protected override void ApplyItemEffects(GameObject picker)
    {
        // Add to inventory for strategic use
        PUBGInventory inventory = picker.GetComponent<PUBGInventory>();
        if (inventory != null)
        {
            // Create PUBG item data
            PUBGItem healingItem = CreatePUBGItem();
            inventory.AddItem(healingItem);
            Debug.Log($"💊 Added {itemName} to inventory");
            return;
        }

        // Fallback to immediate healing
        ApplyImmediateHealing(picker);
    }

    void ApplyImmediateHealing(GameObject picker)
    {
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            if (!providesBoost)
            {
                // Direct healing
                float maxHeal = agent.maxHealth * maxHealthPercent;
                float actualHeal = Mathf.Min(healAmount, maxHeal - agent.currentHealth);

                if (actualHeal > 0)
                {
                    agent.Heal(actualHeal);
                    Debug.Log($"💚 Agent healed {actualHeal} HP with {itemName}");
                }
            }
            else
            {
                // Apply boost effect (implement boost system as needed)
                Debug.Log($"⚡ Agent used {itemName} for boost effect");
            }
        }

        PlayerController player = picker.GetComponent<PlayerController>();
        if (player != null)
        {
            if (!providesBoost)
            {
                float maxHeal = player.maxHealth * maxHealthPercent;
                float actualHeal = Mathf.Min(healAmount, maxHeal - player.currentHealth);

                if (actualHeal > 0)
                {
                    player.Heal(actualHeal);
                    Debug.Log($"💚 Player healed {actualHeal} HP with {itemName}");
                }
            }
        }
    }

    PUBGItem CreatePUBGItem()
    {
        PUBGItem item = ScriptableObject.CreateInstance<PUBGItem>();
        item.itemName = itemName;
        item.itemType = PUBGItem.ItemType.Healing;
        item.healAmount = healAmount;
        item.useTime = useTime;
        item.maxHealthPercent = maxHealthPercent;
        item.rarity = (int)rarity;
        item.canStack = canStack;
        item.maxStackSize = maxStackSize;

        if (providesBoost)
        {
            item.boostAmount = boostAmount;
            item.boostDuration = boostDuration;
        }

        return item;
    }

    public override float GetAIPriority(SquadMateAgent agent)
    {
        float priority = base.GetAIPriority(agent);

        // Higher priority if agent is injured
        float healthPercent = agent.currentHealth / agent.maxHealth;
        if (healthPercent < 0.5f)
        {
            priority *= 3f; // Very high priority when low health
        }
        else if (healthPercent < 0.75f)
        {
            priority *= 2f; // High priority when moderately injured
        }

        // Higher priority for better healing items
        if (healingType == HealingType.MedKit)
        {
            priority *= 1.5f;
        }
        else if (healingType == HealingType.FirstAidKit)
        {
            priority *= 1.2f;
        }

        // Lower priority if agent is in combat and item takes long to use
        if (agent.isInCombat && useTime > 5f)
        {
            priority *= 0.3f;
        }

        return priority;
    }

    protected override void SetRarityColor()
    {
        if (itemRenderer == null) return;

        // Set color based on healing type
        Color healingColor;
        switch (healingType)
        {
            case HealingType.Bandage:
                healingColor = Color.white;
                break;
            case HealingType.FirstAidKit:
                healingColor = Color.green;
                break;
            case HealingType.MedKit:
                healingColor = Color.cyan;
                break;
            case HealingType.EnergyDrink:
                healingColor = Color.yellow;
                break;
            case HealingType.Painkiller:
                healingColor = new Color(1f, 0.5f, 0f); // Orange
                break;
            case HealingType.AdrenalineSyringe:
                healingColor = Color.red;
                break;
            default:
                healingColor = Color.green;
                break;
        }

        itemRenderer.material.color = healingColor;
    }

    // Static factory methods for creating specific healing items
    public static HealingLoot CreateMedKit(GameObject prefab)
    {
        HealingLoot healing = prefab.GetComponent<HealingLoot>();
        healing.itemName = "Med Kit";
        healing.healingType = HealingType.MedKit;
        return healing;
    }

    public static HealingLoot CreateFirstAidKit(GameObject prefab)
    {
        HealingLoot healing = prefab.GetComponent<HealingLoot>();
        healing.itemName = "First Aid Kit";
        healing.healingType = HealingType.FirstAidKit;
        return healing;
    }

    public static HealingLoot CreateEnergyDrink(GameObject prefab)
    {
        HealingLoot healing = prefab.GetComponent<HealingLoot>();
        healing.itemName = "Energy Drink";
        healing.healingType = HealingType.EnergyDrink;
        return healing;
    }
}
