using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;

/// <summary>
/// Creates a simple PUBG training scene that matches the structure you showed
/// </summary>
public class CreateSimplePUBGScene : EditorWindow
{
    [MenuItem("SquadMate AI/🎯 Create Simple PUBG Scene")]
    public static void CreateSimpleScene()
    {
        Debug.Log("🎯 Creating Simple PUBG Training Scene...");
        
        // Create the scene structure
        CreatePUBGTrainingArena();
        
        Debug.Log("✅ Simple PUBG Training Scene created!");
        Debug.Log("📁 Scene structure matches your desired layout");
    }
    
    static void CreatePUBGTrainingArena()
    {
        // Create main arena container
        GameObject arena = new GameObject("PUBGTrainingArena");
        arena.transform.position = Vector3.zero;
        
        // Create agents
        GameObject teammateBot = CreateTeammateBot();
        GameObject enemyBot = CreateEnemyBot();
        
        // Create loot items
        CreateLootItems(arena.transform);
        
        // Create environment
        CreateEnvironment(arena.transform);
        
        // Configure references
        ConfigureAgentReferences(teammateBot, enemyBot, arena);
        
        Debug.Log("🏟️ PUBG Training Arena created with all components");
    }
    
    static GameObject CreateTeammateBot()
    {
        GameObject teammate = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        teammate.name = "TeammateBot";
        teammate.transform.position = new Vector3(-2f, 1f, 0f);
        teammate.transform.localScale = new Vector3(1f, 2f, 1f);
        teammate.tag = "Agent";
        
        // Add ML-Agents components
        BehaviorParameters behaviorParams = teammate.AddComponent<BehaviorParameters>();
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BehaviorType = BehaviorType.Default;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;
        
        // Add SquadMate components
        teammate.AddComponent<SquadMateAgent>();
        teammate.AddComponent<InventorySystem>();
        teammate.AddComponent<HealthSystem>();
        teammate.AddComponent<WeaponSystem>();
        
        // Add Rigidbody for physics
        Rigidbody rb = teammate.AddComponent<Rigidbody>();
        rb.freezeRotation = true;
        
        // Set material color
        Renderer renderer = teammate.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = Color.green;
        renderer.sharedMaterial = mat;
        
        Debug.Log("🤖 TeammateBot created with SquadMate AI");
        return teammate;
    }
    
    static GameObject CreateEnemyBot()
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "EnemyBot";
        enemy.transform.position = new Vector3(10f, 1f, 10f);
        enemy.transform.localScale = new Vector3(1f, 2f, 1f);
        enemy.tag = "Enemy";
        
        // Add enemy components
        enemy.AddComponent<EnemyAI>();
        enemy.AddComponent<HealthSystem>();
        
        // Add Rigidbody for physics
        Rigidbody rb = enemy.AddComponent<Rigidbody>();
        rb.freezeRotation = true;
        
        // Set material color
        Renderer renderer = enemy.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = Color.red;
        renderer.sharedMaterial = mat;
        
        Debug.Log("👹 EnemyBot created");
        return enemy;
    }
    
    static void CreateLootItems(Transform parent)
    {
        // Create weapon pickups
        SimpleWeaponPickup.CreateWeaponPickup("M416", new Vector3(5f, 0.5f, 5f), parent);
        SimpleWeaponPickup.CreateWeaponPickup("UMP45", new Vector3(-5f, 0.5f, 5f), parent);
        SimpleWeaponPickup.CreateWeaponPickup("Kar98k", new Vector3(0f, 0.5f, 8f), parent);
        
        // Create medkit pickups
        SimpleMedkitPickup.CreateMedkitPickup("Medkit", new Vector3(3f, 0.5f, -3f), parent);
        SimpleMedkitPickup.CreateMedkitPickup("First Aid", new Vector3(-3f, 0.5f, -3f), parent);
        SimpleMedkitPickup.CreateMedkitPickup("Energy Drink", new Vector3(0f, 0.5f, -5f), parent);
        
        Debug.Log("🎒 Loot items created (weapons and healing)");
    }
    
    static void CreateEnvironment(Transform parent)
    {
        // Create ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(5f, 1f, 5f);
        ground.transform.SetParent(parent);
        
        // Create some cover objects
        for (int i = 0; i < 5; i++)
        {
            GameObject cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cover.name = $"Cover_{i + 1}";
            cover.transform.position = new Vector3(
                Random.Range(-15f, 15f),
                0.5f,
                Random.Range(-15f, 15f)
            );
            cover.transform.localScale = new Vector3(2f, 1f, 2f);
            cover.transform.SetParent(parent);
            
            // Set cover material
            Renderer renderer = cover.GetComponent<Renderer>();
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = Color.gray;
            renderer.sharedMaterial = mat;
        }
        
        // Create walls around the arena
        CreateWalls(parent);
        
        Debug.Log("🏗️ Environment created with ground, cover, and walls");
    }
    
    static void CreateWalls(Transform parent)
    {
        Vector3[] wallPositions = {
            new Vector3(0f, 2.5f, 25f),   // North
            new Vector3(0f, 2.5f, -25f),  // South
            new Vector3(25f, 2.5f, 0f),   // East
            new Vector3(-25f, 2.5f, 0f)   // West
        };
        
        Vector3[] wallScales = {
            new Vector3(50f, 5f, 1f),     // North/South
            new Vector3(50f, 5f, 1f),     // North/South
            new Vector3(1f, 5f, 50f),     // East/West
            new Vector3(1f, 5f, 50f)      // East/West
        };
        
        for (int i = 0; i < wallPositions.Length; i++)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = $"Wall_{i + 1}";
            wall.transform.position = wallPositions[i];
            wall.transform.localScale = wallScales[i];
            wall.transform.SetParent(parent);
            
            // Set wall material
            Renderer renderer = wall.GetComponent<Renderer>();
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = new Color(0.5f, 0.3f, 0.2f); // Brown
            renderer.sharedMaterial = mat;
        }
    }
    
    static void ConfigureAgentReferences(GameObject teammate, GameObject enemy, GameObject arena)
    {
        // Configure SquadMate agent
        SquadMateAgent agent = teammate.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            // Find and assign environment
            GameEnvironment gameEnv = arena.GetComponent<GameEnvironment>();
            if (gameEnv == null)
            {
                gameEnv = arena.AddComponent<GameEnvironment>();
            }
            
            agent.environment = gameEnv;
            
            // Configure environment
            gameEnv.squadMate = agent;
            gameEnv.environmentSize = new Vector3(50f, 0f, 50f);
            gameEnv.maxEnemies = 1;
            gameEnv.maxMedkits = 3;
            gameEnv.maxWeapons = 3;
        }
        
        // Add dynamic reward system
        if (teammate.GetComponent<DynamicRewardSystem>() == null)
        {
            teammate.AddComponent<DynamicRewardSystem>();
        }
        
        Debug.Log("🔗 Agent references configured");
    }
    
    [MenuItem("SquadMate AI/📦 Create Simple Prefabs")]
    public static void CreateSimplePrefabs()
    {
        Debug.Log("📦 Creating simple prefabs...");
        
        // Ensure Prefabs directory exists
        if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
        {
            AssetDatabase.CreateFolder("Assets", "Prefabs");
        }
        
        // Create TeammateBot prefab
        GameObject teammate = CreateTeammateBot();
        PrefabUtility.SaveAsPrefabAsset(teammate, "Assets/Prefabs/TeammateBot.prefab");
        DestroyImmediate(teammate);
        
        // Create EnemyBot prefab
        GameObject enemy = CreateEnemyBot();
        PrefabUtility.SaveAsPrefabAsset(enemy, "Assets/Prefabs/EnemyBot.prefab");
        DestroyImmediate(enemy);
        
        // Create weapon prefabs
        GameObject m416 = SimpleWeaponPickup.CreateWeaponPickup("M416", Vector3.zero);
        PrefabUtility.SaveAsPrefabAsset(m416, "Assets/Prefabs/M416.prefab");
        DestroyImmediate(m416);
        
        GameObject ump45 = SimpleWeaponPickup.CreateWeaponPickup("UMP45", Vector3.zero);
        PrefabUtility.SaveAsPrefabAsset(ump45, "Assets/Prefabs/UMP45.prefab");
        DestroyImmediate(ump45);
        
        // Create medkit prefab
        GameObject medkit = SimpleMedkitPickup.CreateMedkitPickup("Medkit", Vector3.zero);
        PrefabUtility.SaveAsPrefabAsset(medkit, "Assets/Prefabs/Medkit.prefab");
        DestroyImmediate(medkit);
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        Debug.Log("✅ Simple prefabs created in Assets/Prefabs/");
    }
}
