using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Simplified inventory system interface that bridges to the advanced PUBGInventory system
/// Provides simple AddWeapon/AddHealing methods for compatibility with basic pickup scripts
/// </summary>
public class InventorySystem : MonoBehaviour
{
    [Header("🎒 Simple Inventory Interface")]
    public List<string> weapons = new List<string>();
    public List<string> healingItems = new List<string>();
    public int maxWeapons = 2;
    public int maxHealingItems = 5;
    
    private PUBGInventory pubgInventory;
    private PUBGItemDatabase itemDatabase;
    
    void Start()
    {
        // Get or create PUBG inventory system
        pubgInventory = GetComponent<PUBGInventory>();
        if (pubgInventory == null)
        {
            pubgInventory = gameObject.AddComponent<PUBGInventory>();
        }
        
        // Find item database
        itemDatabase = FindObjectOfType<PUBGItemDatabase>();
        if (itemDatabase == null)
        {
            // Create a temporary database if none exists
            GameObject dbObj = new GameObject("TempItemDatabase");
            itemDatabase = dbObj.AddComponent<PUBGItemDatabase>();
            itemDatabase.InitializeDefaultItems();
        }
    }
    
    /// <summary>
    /// Add a weapon to the inventory (simplified interface)
    /// </summary>
    public bool AddWeapon(string weaponName)
    {
        if (weapons.Count >= maxWeapons)
        {
            Debug.Log($"❌ Cannot add {weaponName} - weapon slots full");
            return false;
        }
        
        // Find the weapon in the PUBG database
        PUBGItem weaponItem = FindWeaponByName(weaponName);
        if (weaponItem != null)
        {
            bool success = pubgInventory.TryAddItem(weaponItem);
            if (success)
            {
                weapons.Add(weaponName);
                Debug.Log($"🔫 Added {weaponName} to inventory");
                return true;
            }
        }
        else
        {
            // Fallback: add to simple list even if not in database
            weapons.Add(weaponName);
            Debug.Log($"🔫 Added {weaponName} to simple inventory");
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Add a healing item to the inventory (simplified interface)
    /// </summary>
    public bool AddHealing(string healingName)
    {
        if (healingItems.Count >= maxHealingItems)
        {
            Debug.Log($"❌ Cannot add {healingName} - healing slots full");
            return false;
        }
        
        // Find the healing item in the PUBG database
        PUBGItem healingItem = FindHealingByName(healingName);
        if (healingItem != null)
        {
            bool success = pubgInventory.TryAddItem(healingItem);
            if (success)
            {
                healingItems.Add(healingName);
                Debug.Log($"💊 Added {healingName} to inventory");
                return true;
            }
        }
        else
        {
            // Fallback: add to simple list even if not in database
            healingItems.Add(healingName);
            Debug.Log($"💊 Added {healingName} to simple inventory");
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Check if inventory has a specific weapon
    /// </summary>
    public bool HasWeapon(string weaponName)
    {
        return weapons.Contains(weaponName) || 
               (pubgInventory != null && pubgInventory.HasItem(weaponName));
    }
    
    /// <summary>
    /// Check if inventory has a specific healing item
    /// </summary>
    public bool HasHealing(string healingName)
    {
        return healingItems.Contains(healingName) || 
               (pubgInventory != null && pubgInventory.HasItem(healingName));
    }
    
    /// <summary>
    /// Remove a weapon from inventory
    /// </summary>
    public bool RemoveWeapon(string weaponName)
    {
        if (weapons.Remove(weaponName))
        {
            if (pubgInventory != null)
            {
                pubgInventory.RemoveItem(weaponName, 1);
            }
            Debug.Log($"🔫 Removed {weaponName} from inventory");
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// Remove a healing item from inventory
    /// </summary>
    public bool RemoveHealing(string healingName)
    {
        if (healingItems.Remove(healingName))
        {
            if (pubgInventory != null)
            {
                pubgInventory.RemoveItem(healingName, 1);
            }
            Debug.Log($"💊 Removed {healingName} from inventory");
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// Get current weapon count
    /// </summary>
    public int GetWeaponCount()
    {
        return weapons.Count;
    }
    
    /// <summary>
    /// Get current healing item count
    /// </summary>
    public int GetHealingCount()
    {
        return healingItems.Count;
    }
    
    /// <summary>
    /// Check if inventory is full
    /// </summary>
    public bool IsInventoryFull()
    {
        return weapons.Count >= maxWeapons && healingItems.Count >= maxHealingItems;
    }
    
    /// <summary>
    /// Find weapon by name in PUBG database
    /// </summary>
    private PUBGItem FindWeaponByName(string weaponName)
    {
        if (itemDatabase == null) return null;
        
        foreach (PUBGItem weapon in itemDatabase.weapons)
        {
            if (weapon.itemName.Equals(weaponName, System.StringComparison.OrdinalIgnoreCase))
            {
                return weapon;
            }
        }
        return null;
    }
    
    /// <summary>
    /// Find healing item by name in PUBG database
    /// </summary>
    private PUBGItem FindHealingByName(string healingName)
    {
        if (itemDatabase == null) return null;
        
        foreach (PUBGItem healing in itemDatabase.healingItems)
        {
            if (healing.itemName.Equals(healingName, System.StringComparison.OrdinalIgnoreCase) ||
                healingName.Equals("Medkit", System.StringComparison.OrdinalIgnoreCase) && healing.itemName.Contains("Med"))
            {
                return healing;
            }
        }
        return null;
    }
    
    /// <summary>
    /// Debug print inventory contents
    /// </summary>
    public void DebugPrintInventory()
    {
        Debug.Log($"📦 Simple Inventory - Weapons: {weapons.Count}/{maxWeapons}, Healing: {healingItems.Count}/{maxHealingItems}");
        
        if (weapons.Count > 0)
        {
            Debug.Log($"🔫 Weapons: {string.Join(", ", weapons)}");
        }
        
        if (healingItems.Count > 0)
        {
            Debug.Log($"💊 Healing: {string.Join(", ", healingItems)}");
        }
        
        // Also print PUBG inventory if available
        if (pubgInventory != null)
        {
            pubgInventory.DebugPrintInventory();
        }
    }
}
