{"count": 1, "self": 1930.4566783999999, "total": 1931.6293421999999, "children": {"InitializeActuators": {"count": 3, "self": 0.003025, "total": 0.003025, "children": null}, "InitializeSensors": {"count": 3, "self": 0.0019973, "total": 0.0019973, "children": null}, "AgentSendState": {"count": 247394, "self": 0.2465424, "total": 0.2465424, "children": null}, "DecideAction": {"count": 247394, "self": 0.8317943999999999, "total": 0.8317943999999999, "children": null}, "AgentAct": {"count": 247394, "self": 0.0893375, "total": 0.0893375, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}