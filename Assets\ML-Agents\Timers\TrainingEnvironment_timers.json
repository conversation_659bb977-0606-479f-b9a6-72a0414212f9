{"count": 1, "self": 1640.1196032, "total": 1644.7988043999999, "children": {"InitializeActuators": {"count": 4, "self": 0.0029917999999999998, "total": 0.0029917999999999998, "children": null}, "InitializeSensors": {"count": 4, "self": 0.001995, "total": 0.001995, "children": null}, "AgentSendState": {"count": 891874, "self": 0.8595786999999999, "total": 0.8595786999999999, "children": null}, "DecideAction": {"count": 891874, "self": 3.5186583999999996, "total": 3.5186583, "children": null}, "AgentAct": {"count": 891874, "self": 0.2959826, "total": 0.2959826, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}